import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="product-filter"
export default class extends Controller {
  static targets = ["searchInput", "pageInput", "form", "searchBtn"]
  static values = { debounceDelay: { type: Number, default: 300 } }

  connect() {
    console.log("🔍 Product Filter Controller connected!")
    console.log("🔍 Search input target:", this.hasSearchInputTarget ? "Found" : "NOT FOUND")
    console.log("🔍 Form target:", this.hasFormTarget ? "Found" : "NOT FOUND")
    this.searchTimeout = null
    this.setupKeyboardShortcuts()

    // Test event binding and add manual event listeners as backup
    if (this.hasSearchInputTarget) {
      console.log("🔍 Setting up event listeners on search input")

      // Store the last value to detect changes
      this.lastValue = this.searchInputTarget.value

      // Add manual event listeners as backup
      this.searchInputTarget.addEventListener('input', this.handleInput.bind(this))
      this.searchInputTarget.addEventListener('keyup', this.handleKeyup.bind(this))
      this.searchInputTarget.addEventListener('keydown', this.handleKeydown.bind(this))
      this.searchInputTarget.addEventListener('paste', this.handlePaste.bind(this))
      this.searchInputTarget.addEventListener('change', this.handleChange.bind(this))

      // Set up a polling mechanism as ultimate fallback
      this.setupValuePolling()
    }
  }

  disconnect() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }

    this.removeKeyboardShortcuts()

    // Clean up manual event listeners
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.removeEventListener('input', this.handleInput.bind(this))
      this.searchInputTarget.removeEventListener('keyup', this.handleKeyup.bind(this))
      this.searchInputTarget.removeEventListener('keydown', this.handleKeydown.bind(this))
      this.searchInputTarget.removeEventListener('change', this.handleChange.bind(this))
      this.searchInputTarget.removeEventListener('paste', this.handlePaste.bind(this))
    }
  }

  // Handle search input with debouncing
  search(event) {
    const currentValue = event.target.value
    console.log("🔍 Search method called!", event.type, "Value:", currentValue)

    // Update last value
    this.lastValue = currentValue

    clearTimeout(this.searchTimeout)

    this.searchTimeout = setTimeout(() => {
      console.log("🔍 Executing search after debounce")
      this.resetToFirstPage()
      this.submitForm()
    }, this.debounceDelayValue)
  }

  // Handle paste events
  paste(event) {
    // Handle paste events with a slight delay to ensure content is processed
    setTimeout(() => {
      this.search(event)
    }, 10)
  }

  // Manual event handlers as backup
  handleInput(event) {
    console.log("🔍 Manual input handler called!", event.target.value)
    this.search(event)
  }

  handleKeyup(event) {
    console.log("🔍 Manual keyup handler called!", event.target.value)
    this.search(event)
  }

  handleKeydown(event) {
    console.log("🔍 Manual keydown handler called!", event.target.value)
    // Small delay to let the character be processed
    setTimeout(() => {
      if (this.searchInputTarget.value !== this.lastValue) {
        console.log("🔍 Value changed in keydown:", this.searchInputTarget.value)
        this.search(event)
      }
    }, 1)
  }

  handleChange(event) {
    console.log("🔍 Manual change handler called!", event.target.value)
    this.search(event)
  }

  handlePaste(event) {
    console.log("🔍 Manual paste handler called!")
    this.paste(event)
  }

  // Polling mechanism as ultimate fallback
  setupValuePolling() {
    this.pollingInterval = setInterval(() => {
      if (this.hasSearchInputTarget) {
        const currentValue = this.searchInputTarget.value
        if (currentValue !== this.lastValue) {
          console.log("🔍 Polling detected value change:", this.lastValue, "→", currentValue)
          this.lastValue = currentValue
          this.search({ target: this.searchInputTarget, type: 'polling' })
        }
      }
    }, 100) // Check every 100ms
  }

  // Handle filter changes (status, sort, etc.)
  filterChange(event) {
    this.resetToFirstPage()
    this.submitForm()
  }

  // Clear all filters
  clearFilters() {
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.value = ''
    }
    
    // Reset filter selects to defaults
    const statusSelect = this.formTarget.querySelector('select[name="status"]')
    const sortBySelect = this.formTarget.querySelector('select[name="sort_by"]')
    const sortOrderSelect = this.formTarget.querySelector('select[name="sort_order"]')
    
    if (statusSelect) statusSelect.value = 'active'
    if (sortBySelect) sortBySelect.value = 'name'
    if (sortOrderSelect) sortOrderSelect.value = 'asc'
    
    this.resetToFirstPage()
    this.submitForm()
  }

  // Focus search input (for keyboard shortcut)
  focusSearch() {
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.focus()
      this.searchInputTarget.select()
    }
  }

  // Private methods
  resetToFirstPage() {
    if (this.hasPageInputTarget) {
      this.pageInputTarget.value = 1
    }
  }

  submitForm() {
    if (this.hasFormTarget) {
      this.showLoadingState()
      this.formTarget.requestSubmit()
    }
  }

  showLoadingState() {
    if (this.hasSearchBtnTarget) {
      this.searchBtnTarget.innerHTML = '⏳'
      this.searchBtnTarget.disabled = true
    }
  }

  hideLoadingState() {
    if (this.hasSearchBtnTarget) {
      this.searchBtnTarget.innerHTML = '🔍'
      this.searchBtnTarget.disabled = false
    }
  }

  // Keyboard shortcuts
  setupKeyboardShortcuts() {
    this.keydownHandler = this.handleKeydown.bind(this)
    document.addEventListener('keydown', this.keydownHandler)
  }

  removeKeyboardShortcuts() {
    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler)
    }
  }

  handleKeydown(event) {
    // Ctrl/Cmd + K to focus search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      this.focusSearch()
    }

    // Escape to clear search (only if search input is focused)
    if (event.key === 'Escape' && document.activeElement === this.searchInputTarget) {
      this.clearFilters()
    }
  }

  // Turbo events
  formSubmitStart() {
    this.showLoadingState()
  }

  formSubmitEnd() {
    this.hideLoadingState()
  }

  // Update stats when products load
  updateStats() {
    const productsCount = document.querySelectorAll('.product-card').length
    const statsMain = document.querySelector('.stats-main')
    const totalCount = statsMain?.dataset.totalCount
    
    if (statsMain && totalCount) {
      statsMain.textContent = `Showing ${productsCount} of ${totalCount} products`
    }
  }
}
