import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="product-filter"
export default class extends Controller {
  static targets = ["searchInput", "pageInput", "form", "searchBtn"]
  static values = { debounceDelay: { type: Number, default: 300 } }

  connect() {
    console.log("🔍 Product Filter Controller connected!")
    this.searchTimeout = null
    this.setupKeyboardShortcuts()
  }

  disconnect() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }
    this.removeKeyboardShortcuts()
  }

  // Handle search input with debouncing
  search(event) {
    console.log("🔍 Search triggered!", event.type || 'unknown')

    clearTimeout(this.searchTimeout)

    this.searchTimeout = setTimeout(() => {
      console.log("🔍 Executing search")
      this.resetToFirstPage()
      this.submitForm()
    }, this.debounceDelayValue)
  }

  // Handle paste events
  paste(event) {
    setTimeout(() => {
      this.search(event)
    }, 10)
  }

  // Handle filter changes (status, sort, etc.)
  filterChange(event) {
    this.resetToFirstPage()
    this.submitForm()
  }

  // Clear all filters
  clearFilters() {
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.value = ''
    }

    // Reset filter selects to defaults
    const form = this.element.closest('form')
    if (form) {
      const statusSelect = form.querySelector('select[name="status"]')
      const sortBySelect = form.querySelector('select[name="sort_by"]')
      const sortOrderSelect = form.querySelector('select[name="sort_order"]')

      if (statusSelect) statusSelect.value = 'active'
      if (sortBySelect) sortBySelect.value = 'name'
      if (sortOrderSelect) sortOrderSelect.value = 'asc'
    }

    this.resetToFirstPage()
    this.submitForm()
  }

  // Focus search input (for keyboard shortcut)
  focusSearch() {
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.focus()
      this.searchInputTarget.select()
    }
  }

  // Private methods
  resetToFirstPage() {
    const pageInput = this.pageInputTarget || this.element.closest('form')?.querySelector('input[name="page"]')
    if (pageInput) {
      pageInput.value = 1
    }
  }

  submitForm() {
    const form = this.element.closest('form') || this.formTarget
    if (form) {
      this.showLoadingState()
      form.requestSubmit()
    }
  }

  showLoadingState() {
    if (this.hasSearchBtnTarget) {
      this.searchBtnTarget.innerHTML = '⏳'
      this.searchBtnTarget.disabled = true
    }
  }

  hideLoadingState() {
    if (this.hasSearchBtnTarget) {
      this.searchBtnTarget.innerHTML = '🔍'
      this.searchBtnTarget.disabled = false
    }
  }

  // Keyboard shortcuts
  setupKeyboardShortcuts() {
    this.keydownHandler = this.handleKeydown.bind(this)
    document.addEventListener('keydown', this.keydownHandler)
  }

  removeKeyboardShortcuts() {
    if (this.keydownHandler) {
      document.removeEventListener('keydown', this.keydownHandler)
    }
  }

  handleKeydown(event) {
    // Ctrl/Cmd + K to focus search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      this.focusSearch()
    }

    // Escape to clear search (only if search input is focused)
    if (event.key === 'Escape' && document.activeElement === this.searchInputTarget) {
      this.clearFilters()
    }
  }

  // Turbo events
  formSubmitStart() {
    this.showLoadingState()
  }

  formSubmitEnd() {
    this.hideLoadingState()
  }

  // Update stats when products load
  updateStats() {
    const productsCount = document.querySelectorAll('.product-card').length
    const statsMain = document.querySelector('.stats-main')
    const totalCount = statsMain?.dataset.totalCount
    
    if (statsMain && totalCount) {
      statsMain.textContent = `Showing ${productsCount} of ${totalCount} products`
    }
  }
}
