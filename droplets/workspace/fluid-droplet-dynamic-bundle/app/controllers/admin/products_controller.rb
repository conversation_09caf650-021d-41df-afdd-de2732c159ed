# frozen_string_literal: true

module Admin
  # Controller for managing product assignment to bundle categories
  # Handles product search, assignment, and ordering within categories
  class ProductsController < ApplicationController
    layout 'bundle_admin'
    
    # before_action :authenticate_admin!  # Temporarily disabled for testing
    before_action :set_bundle_and_category, except: [:search, :search_enhanced]
    before_action :set_product, only: [:assign, :unassign, :set_default, :move_up, :move_down]

    # GET /admin/products/search
    # API endpoint for product search in bundle wizard - optimized for large catalogs
    def search
      search_term = params[:q] || params[:search] || ''
      page = params[:page] || 1
      per_page = params[:per_page] || 50  # Optimized for large catalogs like Yoli7 (2700+ products)

      # Additional filters for better product management
      status_filter = params[:status] || 'active'
      price_min = params[:price_min]
      price_max = params[:price_max]
      sort_by = params[:sort_by] || 'name'
      sort_order = params[:sort_order] || 'asc'

      Rails.logger.info("PRODUCTS API: Enhanced search - term: '#{search_term}', page: #{page}, filters: status=#{status_filter}, sort=#{sort_by}")

      # Check if Fluid API is configured
      begin
        setting = Setting.fluid_api
        if setting.blank? || setting.values['api_key'].blank?
          Rails.logger.error("PRODUCTS API: Fluid API not configured")
          render json: {
            success: false,
            error: "Fluid API not configured. Please configure the API settings.",
            products: [],
            pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
          }, status: 500
          return
        end
      rescue => e
        Rails.logger.error("PRODUCTS API: Fluid API setting error: #{e.message}")
        render json: {
          success: false,
          error: "Fluid API configuration error: #{e.message}",
          products: [],
          pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
        }, status: 500
        return
      end

      # Call Fluid API with enhanced parameters
      begin
        Rails.logger.info("PRODUCTS API: Fetching products with enhanced filters...")

        service_params = {
          page: page.to_i,
          per_page: per_page.to_i,
          company: @company,
          status: status_filter,
          sort_by: sort_by,
          sort_order: sort_order
        }

        # Add price filters if provided
        service_params[:price_min] = price_min.to_f if price_min.present?
        service_params[:price_max] = price_max.to_f if price_max.present?

        result = if search_term.present?
          Fluid::ProductsService.call(
            **service_params.merge(action: :search, query: search_term)
          )
        else
          Fluid::ProductsService.call(
            **service_params.merge(action: :list)
          )
        end

        if result.success?
          products = result.data[:products] || []
          pagination = result.data[:pagination] || {}

          Rails.logger.info("PRODUCTS API: Successfully retrieved #{products.length} products (#{pagination[:total_count] || 0} total)")

          # Products are already normalized by the service, no need to re-format
          formatted_products = products

          render json: {
            success: true,
            products: formatted_products,
            pagination: {
              current_page: pagination[:current_page] || page.to_i,
              per_page: pagination[:per_page] || per_page.to_i,
              total_pages: pagination[:total_pages] || 1,
              total_count: pagination[:total_count] || formatted_products.length,
              has_next_page: pagination[:has_next_page] || false,
              has_prev_page: pagination[:has_prev_page] || false
            },
            filters: {
              search: search_term,
              status: status_filter,
              price_range: [price_min, price_max].compact,
              sort: "#{sort_by} #{sort_order}"
            }
          }
          return
        else
          Rails.logger.error("PRODUCTS API: Service failed - #{result.error}")
          render json: {
            success: false,
            error: result.error,
            products: [],
            pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
          }, status: 500
          return
        end
      rescue => e
        Rails.logger.error("PRODUCTS API: Exception - #{e.message}")
        render json: {
          success: false,
          error: e.message,
          products: [],
          pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
        }, status: 500
        return
      end
    rescue => e
      Rails.logger.error("PRODUCTS API: Outer exception - #{e.message}")
      render json: {
        success: false,
        error: e.message,
        products: [],
        pagination: { page: 1, per_page: per_page.to_i, total: 0, total_pages: 0 }
      }, status: 500
    end

    # GET /admin/products/search_enhanced
    # Enhanced search endpoint with real-time filtering for large catalogs
    def search_enhanced
      search_term = params[:q] || params[:search] || ''
      page = params[:page] || 1
      per_page = params[:per_page] || 50

      Rails.logger.info("ENHANCED SEARCH: term='#{search_term}', page=#{page}")

      # Use the same enhanced search logic as the main search method
      begin
        setting = Setting.fluid_api
        if setting.blank? || setting.values['api_key'].blank?
          render json: {
            success: false,
            error: "Fluid API not configured",
            products: [],
            pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
          }
          return
        end

        service_params = {
          page: page.to_i,
          per_page: per_page.to_i,
          company: @company,
          status: params[:status] || 'active',
          sort_by: params[:sort_by] || 'name',
          sort_order: params[:sort_order] || 'asc'
        }

        result = if search_term.present?
          Fluid::ProductsService.call(
            **service_params.merge(action: :search, query: search_term)
          )
        else
          Fluid::ProductsService.call(
            **service_params.merge(action: :list)
          )
        end

        if result.success?
          products = result.data[:products] || []
          pagination = result.data[:pagination] || {}

          render json: {
            success: true,
            products: products,
            pagination: pagination,
            total_count: pagination[:total_count] || products.length
          }
        else
          render json: {
            success: false,
            error: result.error,
            products: [],
            pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
          }
        end
      rescue => e
        Rails.logger.error("ENHANCED SEARCH ERROR: #{e.message}")
        render json: {
          success: false,
          error: e.message,
          products: [],
          pagination: { page: page.to_i, per_page: per_page.to_i, total: 0, total_pages: 0 }
        }
      end
    end

    # GET /admin/bundles/:bundle_id/categories/:category_id/products
    # Shows product assignment interface with enhanced search for large catalogs
    def index
      @page = params[:page]&.to_i || 1
      @per_page = params[:per_page]&.to_i || 50  # Increased for better performance with large catalogs
      @search_query = params[:search] || ""
      @status_filter = params[:status] || 'active'
      @sort_by = params[:sort_by] || 'name'
      @sort_order = params[:sort_order] || 'asc'

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      # Enhanced product search with filters - optimized for large catalogs like Yoli7
      service_params = {
        page: @page,
        per_page: @per_page,
        company: @company,
        status: @status_filter,
        sort_by: @sort_by,
        sort_order: @sort_order
      }

      result = if @search_query.present?
        Rails.logger.info("PRODUCTS INDEX: Searching with term: '#{@search_query}', page: #{@page}")
        Fluid::ProductsService.call(
          **service_params.merge(action: :search, query: @search_query)
        )
      else
        Rails.logger.info("PRODUCTS INDEX: Listing products, page: #{@page}")
        Fluid::ProductsService.call(
          **service_params.merge(action: :list)
        )
      end

      if result.success?
        @available_products = result.data[:products] || []
        @pagination = result.data[:pagination] || {}
        @total_count = @pagination[:total_count] || 0

        Rails.logger.info("PRODUCTS INDEX: Retrieved #{@available_products.length} products (#{@total_count} total)")
      else
        @available_products = []
        @pagination = {}
        @total_count = 0
        flash.now[:error] = "Failed to search products: #{result.error}"
      end

      @assigned_products = @category['products'] || []

      # Add filter options for the UI
      @filter_options = {
        status: [
          ['Active Products', 'active'],
          ['Inactive Products', 'inactive'],
          ['All Products', 'all']
        ],
        sort: [
          ['Name A-Z', 'name_asc'],
          ['Name Z-A', 'name_desc'],
          ['Price Low-High', 'price_asc'],
          ['Price High-Low', 'price_desc'],
          ['Newest First', 'created_at_desc'],
          ['Oldest First', 'created_at_asc']
        ]
      }
    end

    # POST /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/assign
    # Assigns a product to the category
    def assign
      variant_id = params[:variant_id]
      product_id = params[:product_id] || params[:id]

      # Get product details from Fluid API
      product_result = Fluid::ProductsService.call(action: :find, product_id: product_id, company: @company)
      unless product_result.success?
        flash[:error] = "Failed to load product details: #{product_result.error}"
        redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
        return
      end

      product_data = product_result.data[:product]
      variant = product_data['variants']&.find { |v| v['id'] == variant_id }

      unless variant
        flash[:error] = "Product variant not found"
        redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
        return
      end

      # Prepare product data for assignment
      assignment_data = {
        'productId' => product_id,
        'variantId' => variant_id,
        'variantTitle' => variant['title'],
        'variantSku' => variant['sku'],
        'price' => variant['price'],
        'isDefault' => false
      }

      # Use BundleMetadataService to assign product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :assign_product,
        category_id: @category_id,
        product_data: assignment_data
      )

      if result.success?
        flash[:success] = "Product '#{variant['title']}' assigned to category successfully!"
      else
        flash[:error] = "Failed to assign product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to assign product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # DELETE /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/unassign
    # Removes a product from the category
    def unassign
      product_id = params[:product_id] || params[:id]

      # Use BundleMetadataService to unassign product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :unassign_product,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        product_title = result.data[:removed_product]['variantTitle'] rescue 'Product'
        flash[:success] = "#{product_title} removed from category successfully!"
      else
        flash[:error] = "Failed to remove product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to remove product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/set_default
    # Sets a product as the default selection for the category
    def set_default
      product_id = params[:product_id] || params[:id]

      # Use BundleMetadataService to set default product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :set_default_product,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Default product updated successfully!"
      else
        flash[:error] = "Failed to set default product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to set default product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/move_up
    # Moves product up in display order
    def move_up
      product_id = params[:product_id] || params[:id]

      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_product_up,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Product moved up successfully!"
      else
        flash[:error] = "Failed to move product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/move_down
    # Moves product down in display order
    def move_down
      product_id = params[:product_id] || params[:id]

      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_product_down,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Product moved down successfully!"
      else
        flash[:error] = "Failed to move product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    private

    # Set bundle and category from params
    def set_bundle_and_category
      @bundle_id = params[:bundle_id]
      @category_id = params[:category_id]

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      # Load bundle data from API
      result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
      if result.success?
        @bundle = result.data[:bundle]
        categories = @bundle.dig('metadata', 'categories') || []
        @category = categories.find { |cat| cat['categoryId'] == @category_id }
      else
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      unless @category
        flash[:error] = "Category not found"
        redirect_to admin_bundle_categories_path(@bundle_id)
      end
    end

    # Set product for actions that need it
    def set_product
      @product_id = params[:product_id] || params[:id]
    end

    # Placeholder for admin authentication - temporarily disabled for testing
    def authenticate_admin!
      return true # Temporarily allow all access for testing
      # return true if Rails.env.development?
      # redirect_to root_path unless current_user&.admin?
    end


    private

    # Mock available products for search
    def mock_available_products
      [
        {
          'id' => 'prod1',
          'name' => 'Premium Whey Protein',
          'sku' => 'WHEY-PREM-001',
          'description' => 'High-quality whey protein isolate',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var1',
              'title' => 'Whey Protein - Vanilla',
              'sku' => 'WHEY-VAN-001',
              'price' => 49.99,
              'inventory_quantity' => 100
            },
            {
              'id' => 'var2',
              'title' => 'Whey Protein - Chocolate',
              'sku' => 'WHEY-CHO-001',
              'price' => 49.99,
              'inventory_quantity' => 85
            },
            {
              'id' => 'var3',
              'title' => 'Whey Protein - Strawberry',
              'sku' => 'WHEY-STR-001',
              'price' => 49.99,
              'inventory_quantity' => 92
            }
          ]
        },
        {
          'id' => 'prod2',
          'name' => 'Plant-Based Protein',
          'sku' => 'PLANT-PROT-001',
          'description' => 'Organic plant-based protein blend',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var4',
              'title' => 'Plant Protein - Berry',
              'sku' => 'PLANT-BER-001',
              'price' => 54.99,
              'inventory_quantity' => 67
            },
            {
              'id' => 'var5',
              'title' => 'Plant Protein - Vanilla',
              'sku' => 'PLANT-VAN-001',
              'price' => 54.99,
              'inventory_quantity' => 43
            }
          ]
        },
        {
          'id' => 'prod3',
          'name' => 'Energy Pre-Workout',
          'sku' => 'ENERGY-PRE-001',
          'description' => 'High-energy pre-workout formula',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var6',
              'title' => 'Energy Boost - Citrus',
              'sku' => 'ENERGY-CIT-001',
              'price' => 34.99,
              'inventory_quantity' => 156
            },
            {
              'id' => 'var7',
              'title' => 'Energy Boost - Berry',
              'sku' => 'ENERGY-BER-001',
              'price' => 34.99,
              'inventory_quantity' => 134
            }
          ]
        },
        {
          'id' => 'prod4',
          'name' => 'BCAA Recovery',
          'sku' => 'BCAA-REC-001',
          'description' => 'Branched-chain amino acids for recovery',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var8',
              'title' => 'BCAA - Tropical',
              'sku' => 'BCAA-TROP-001',
              'price' => 39.99,
              'inventory_quantity' => 78
            }
          ]
        },
        {
          'id' => 'prod5',
          'name' => 'Creatine Monohydrate',
          'sku' => 'CREAT-MONO-001',
          'description' => 'Pure creatine monohydrate powder',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var9',
              'title' => 'Creatine - Unflavored',
              'sku' => 'CREAT-UNF-001',
              'price' => 24.99,
              'inventory_quantity' => 203
            }
          ]
        }
      ]
    end


  end
end
