<% content_for :title, "Assign Products - #{@category['categoryName']}" %>
<%= stylesheet_link_tag 'modern_product_cards', 'data-turbo-track': 'reload' %>
<%= stylesheet_link_tag 'enhanced_product_search', 'data-turbo-track': 'reload' %>
<%= javascript_include_tag 'enhanced_product_search', 'data-turbo-track': 'reload' %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2>Assign Products to "<%= @category['categoryName'] %>"</h2>
    <p class="text-muted mb-0">
      Bundle: <strong><%= @bundle['name'] %></strong> • 
      Selection Required: <strong><%= @category['selectionQuantity'] %></strong> • 
      Currently Assigned: <strong><%= @assigned_products.length %></strong>
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundle_categories_path(@bundle_id), class: "btn btn-secondary" do %>
      ← Back to Categories
    <% end %>
  </div>
</div>

<div class="product-assignment-container">
  <!-- Left Panel: Product Search -->
  <div class="search-panel">
    <div class="search-header">
      <h3>🔍 Available Products</h3>
      <div class="search-stats">
        <div class="stats-main" data-total-count="<%= @total_count || 0 %>">
          Showing <%= @available_products.length %> of <%= @total_count || 0 %> products
        </div>
        <% if @search_query.present? %>
          <div class="stats-filter">
            Filtered by: "<%= @search_query %>"
          </div>
        <% end %>
        <% if @total_count && @total_count > 1000 %>
          <div class="stats-note">
            ⚡ Large catalog detected (<%= number_with_delimiter(@total_count) %> products) - search optimized for performance
            <% if @total_count > 2500 %>
              <span class="product-count-badge huge">Huge Catalog</span>
            <% elsif @total_count > 1500 %>
              <span class="product-count-badge large">Large Catalog</span>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    
    <!-- Enhanced Search Form with Filters -->
    <div class="search-form">
      <%= form_with url: admin_bundle_category_products_path(@bundle_id, @category_id),
          method: :get,
          local: false,
          html: {
            class: "search-form-inner",
            id: "product-search-form",
            data: {
              turbo_frame: "products-list-frame",
              "product-filter-target": "form"
            }
          } do |form| %>

        <!-- Main Search Input -->
        <div class="search-input-group">
          <%= form.text_field :search,
              value: @search_query,
              placeholder: "Search products by name, SKU, or variant... (optimized for large catalogs)",
              class: "search-input",
              autocomplete: "off",
              data: {
                controller: "product-filter",
                "product-filter-target": "searchInput",
                action: "input->product-filter#search keyup->product-filter#search paste->product-filter#paste manual-search->product-filter#search turbo:submit-start@form->product-filter#formSubmitStart turbo:submit-end@form->product-filter#formSubmitEnd"
              },
              oninput: "this.dispatchEvent(new CustomEvent('manual-search', { bubbles: true }))",
              onkeyup: "this.dispatchEvent(new CustomEvent('manual-search', { bubbles: true }))" %>
          <div class="keyboard-hint">Ctrl+K to focus • Esc to clear</div>
          <%= form.hidden_field :page, value: 1, data: { "product-filter-target": "pageInput" } %>
          <button type="submit" class="search-btn" data-product-filter-target="searchBtn">🔍</button>
        </div>

        <!-- Advanced Filters -->
        <div class="search-filters">
          <div class="filter-row">
            <div class="filter-group">
              <%= form.label :status, "Status:", class: "filter-label" %>
              <%= form.select :status, @filter_options[:status],
                  { selected: @status_filter },
                  {
                    class: "filter-select",
                    data: { action: "change->product-filter#filterChange" }
                  } %>
            </div>

            <div class="filter-group">
              <%= form.label :sort_by, "Sort:", class: "filter-label" %>
              <%= form.select :sort_by, @filter_options[:sort].map { |label, value|
                  sort_field, sort_dir = value.split('_')
                  [label, sort_field]
                },
                { selected: @sort_by },
                {
                  class: "filter-select",
                  data: { action: "change->product-filter#filterChange" }
                } %>
            </div>

            <div class="filter-group">
              <%= form.label :sort_order, "Order:", class: "filter-label" %>
              <%= form.select :sort_order, [['A-Z / Low-High', 'asc'], ['Z-A / High-Low', 'desc']],
                  { selected: @sort_order },
                  {
                    class: "filter-select",
                    data: { action: "change->product-filter#filterChange" }
                  } %>
            </div>

            <div class="filter-group">
              <button type="button"
                      class="clear-filters-btn"
                      data-action="click->product-filter#clearFilters">
                🗑️ Clear Filters
              </button>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    
    <!-- Available Products List -->
    <%= turbo_frame_tag "products-list-frame" do %>
      <div class="products-list">
        <% if @available_products.any? %>
          <% @available_products.each do |product| %>
          <div class="product-card available" data-product-id="<%= product['id'] %>">
            <div class="product-header">
              <h4 class="product-name"><%= product['name'] %></h4>
              <div class="product-sku">SKU: <code><%= product['sku'] %></code></div>
            </div>

            <div class="product-description">
              <%= truncate(product['description'], length: 80) %>
            </div>

            <div class="variants-list">
              <% product['variants'].each do |variant| %>
                <div class="variant-item">
                  <div class="variant-info">
                    <span class="variant-title"><%= variant['title'] %></span>
                    <span class="variant-price">$<%= variant['price'] %></span>
                    <span class="variant-stock">
                      <% if variant['inventory_quantity'] > 0 %>
                        ✅ <%= variant['inventory_quantity'] %> in stock
                      <% else %>
                        ❌ Out of stock
                      <% end %>
                    </span>
                  </div>

                  <div class="variant-actions">
                    <% if @assigned_products.any? { |product| product['variantId'] == variant['id'] } %>
                      <span class="assigned-badge">✅ Assigned</span>
                    <% else %>
                      <%= link_to assign_admin_bundle_category_product_path(@bundle_id, @category_id, product['id']),
                          method: :post,
                          params: { variant_id: variant['id'] },
                          class: "assign-btn",
                          title: "Assign to category",
                          data: { turbo_method: :post } do %>
                        ➕ Assign
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="empty-search">
          <div class="empty-icon">🔍</div>
          <h4>No products found</h4>
          <p>Try adjusting your search terms or browse all products.</p>
          <% if @search_query.present? %>
            <%= link_to "Clear search", admin_bundle_category_products_path(@bundle_id, @category_id), 
                class: "btn btn-outline-primary" %>
          <% end %>
        </div>
      <% end %>

      <!-- Pagination Controls -->
      <% if @pagination && @pagination[:total_pages] > 1 %>
        <div class="pagination-controls">
          <div class="pagination-info">
            Showing <%= (@pagination[:current_page] - 1) * @pagination[:per_page] + 1 %>-<%= [@pagination[:current_page] * @pagination[:per_page], @pagination[:total_count]].min %>
            of <%= @pagination[:total_count] %> products
          </div>

          <div class="pagination-buttons">
            <% if @pagination[:current_page] > 1 %>
              <%= link_to admin_bundle_category_products_path(@bundle_id, @category_id, search: @search_query, page: @pagination[:current_page] - 1),
                  class: "pagination-btn",
                  data: { turbo_frame: "products-list-frame" } do %>
                ← Previous
              <% end %>
            <% end %>

            <span class="page-info">Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %></span>

            <% if @pagination[:current_page] < @pagination[:total_pages] %>
              <%= link_to admin_bundle_category_products_path(@bundle_id, @category_id, search: @search_query, page: @pagination[:current_page] + 1),
                  class: "pagination-btn",
                  data: { turbo_frame: "products-list-frame" } do %>
                Next →
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    <% end %>
  </div>
  
  <!-- Right Panel: Assigned Products -->
  <div class="assigned-panel">
    <div class="assigned-header">
      <h3>📦 Assigned Products</h3>
      <div class="assigned-stats">
        <%= @assigned_products.length %> of <%= @category['selectionQuantity'] %> required
        <% if @assigned_products.length < @category['selectionQuantity'] %>
          <span class="status-warning">⚠️ Need <%= @category['selectionQuantity'] - @assigned_products.length %> more</span>
        <% elsif @assigned_products.length > @category['selectionQuantity'] %>
          <span class="status-info">ℹ️ <%= @assigned_products.length - @category['selectionQuantity'] %> extra options</span>
        <% else %>
          <span class="status-success">✅ Complete</span>
        <% end %>
      </div>
    </div>
    
    <div class="assigned-products-list">
      <% if @assigned_products.any? %>
        <% @assigned_products.sort_by { |p| p['displayOrder'] || 0 }.each_with_index do |product, index| %>
          <div class="assigned-product-card" data-product-id="<%= product['productId'] %>">
            <div class="assigned-product-header">
              <div class="product-info">
                <h4 class="product-title"><%= product['variantTitle'] %></h4>
                <div class="product-details">
                  <code><%= product['variantSku'] %></code>
                  <span class="product-price">$<%= product['price'] %></span>
                  <% if product['isDefault'] %>
                    <span class="default-badge">⭐ Default</span>
                  <% end %>
                </div>
              </div>
              
              <div class="product-controls">
                <div class="order-controls">
                  <% unless index == 0 %>
                    <%= link_to move_up_admin_bundle_category_product_path(@bundle_id, @category_id, product['productId']), 
                        method: :patch,
                        class: "order-btn",
                        title: "Move up",
                        data: { turbo_method: :patch } do %>
                      ⬆️
                    <% end %>
                  <% end %>
                  
                  <% unless index == @assigned_products.length - 1 %>
                    <%= link_to move_down_admin_bundle_category_product_path(@bundle_id, @category_id, product['productId']), 
                        method: :patch,
                        class: "order-btn",
                        title: "Move down",
                        data: { turbo_method: :patch } do %>
                      ⬇️
                    <% end %>
                  <% end %>
                </div>
                
                <div class="action-controls">
                  <% unless product['isDefault'] %>
                    <%= link_to set_default_admin_bundle_category_product_path(@bundle_id, @category_id, product['productId']), 
                        method: :patch,
                        class: "default-btn",
                        title: "Set as default",
                        data: { turbo_method: :patch } do %>
                      ⭐
                    <% end %>
                  <% end %>
                  
                  <%= link_to unassign_admin_bundle_category_product_path(@bundle_id, @category_id, product['productId']), 
                      method: :delete,
                      class: "remove-btn",
                      title: "Remove from category",
                      data: { 
                        confirm: "Remove '#{product['variantTitle']}' from this category?",
                        turbo_method: :delete
                      } do %>
                    🗑️
                  <% end %>
                </div>
              </div>
            </div>
            
            <div class="product-order-indicator">
              Order: <%= index + 1 %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="empty-assigned">
          <div class="empty-icon">📦</div>
          <h4>No products assigned yet</h4>
          <p>Search and assign products from the left panel to build your category.</p>
          <div class="assignment-tips">
            <h5>💡 Tips:</h5>
            <ul>
              <li>You need <strong><%= @category['selectionQuantity'] %></strong> products for this category</li>
              <li>Set one product as <strong>default</strong> for better UX</li>
              <li>Order products by popularity or preference</li>
            </ul>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<style>
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .product-assignment-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    min-height: 600px;
  }
  
  .search-panel,
  .assigned-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .search-header,
  .assigned-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .search-header h3,
  .assigned-header h3 {
    margin: 0;
    font-size: 18px;
    color: #495057;
  }
  
  .search-stats,
  .assigned-stats {
    font-size: 14px;
    color: #6c757d;
  }

  .search-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .stats-main {
    font-weight: 600;
    color: #495057;
  }

  .stats-filter {
    font-style: italic;
    color: #007bff;
  }

  .stats-note {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
  }

  /* Enhanced Search Filters */
  .search-filters {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
  }

  .filter-row {
    display: flex;
    gap: 16px;
    align-items: end;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
  }

  .filter-label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin: 0;
  }

  .filter-select {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    background: white;
    cursor: pointer;
  }

  .filter-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .clear-filters-btn {
    padding: 6px 12px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .clear-filters-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
  }
  
  .status-warning {
    color: #856404;
    background: #fff3cd;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .status-info {
    color: #0c5460;
    background: #d1ecf1;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .status-success {
    color: #155724;
    background: #d4edda;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
  }
  
  .search-form {
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
  }
  
  .search-input-group {
    display: flex;
    gap: 8px;
  }
  
  .search-input {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
  
  .search-btn {
    padding: 10px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
  }
  
  .search-btn:hover {
    background: #0056b3;
  }
  
  .products-list,
  .assigned-products-list {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    max-height: calc(100vh - 300px);
  }
  
  .product-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
  }

  .product-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .product-header {
    margin-bottom: 8px;
  }

  .product-name {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: #495057;
  }

  .product-sku {
    font-size: 12px;
    color: #6c757d;
  }

  .product-description {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 12px;
    line-height: 1.4;
  }

  .variants-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .variant-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
  }

  .variant-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .variant-title {
    font-weight: 500;
    color: #495057;
  }

  .variant-price {
    color: #28a745;
    font-weight: 600;
  }

  .variant-stock {
    font-size: 11px;
    color: #6c757d;
  }

  .assign-btn {
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .assign-btn:hover {
    background: #0056b3;
    color: white;
    transform: translateY(-1px);
  }

  .assigned-badge {
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .assigned-product-card {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    position: relative;
  }
  
  .assigned-product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .product-title {
    margin: 0 0 8px 0;
    font-size: 15px;
    color: #2e7d32;
  }
  
  .product-details {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 12px;
  }
  
  .product-price {
    color: #28a745;
    font-weight: 600;
  }
  
  .default-badge {
    background: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
  }
  
  .product-controls {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .order-controls {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .order-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 20px;
    background: #e9ecef;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
  }
  
  .order-btn:hover {
    background: #007bff;
    transform: scale(1.1);
  }
  
  .action-controls {
    display: flex;
    gap: 4px;
  }
  
  .default-btn,
  .remove-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
  }
  
  .default-btn {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
  }
  
  .default-btn:hover {
    background: #ffc107;
    transform: translateY(-1px);
  }
  
  .remove-btn {
    background: #ffebee;
    border: 1px solid #ffcdd2;
  }
  
  .remove-btn:hover {
    background: #ffcdd2;
    transform: translateY(-1px);
  }
  
  .product-order-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
  }
  
  .empty-search,
  .empty-assigned {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
  }
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .assignment-tips {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-top: 20px;
    text-align: left;
  }
  
  .assignment-tips h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
  }
  
  .assignment-tips ul {
    margin: 0;
    padding-left: 20px;
    font-size: 13px;
  }
  
  .assignment-tips li {
    margin-bottom: 4px;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
  }

  /* Pagination Styles */
  .pagination-controls {
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pagination-info {
    font-size: 13px;
    color: #6c757d;
  }

  .pagination-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .pagination-btn {
    background: #007bff;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .pagination-btn:hover {
    background: #0056b3;
    color: white;
    transform: translateY(-1px);
  }

  .page-info {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
  }
</style>

<script>
// Minimal script for Turbo compatibility - main functionality handled by Stimulus controller
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔍 Product search page loaded - using Stimulus controller for filtering');
});
</script>


